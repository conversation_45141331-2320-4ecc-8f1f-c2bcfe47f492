<template>
  <q-page class="page-container">
    <div class="content-wrapper">
      <!-- 标题区域 -->
      <div class="header-section">
        <h1 class="main-title">Markdown 智能标注器</h1>
        <p class="subtitle">
          拖拽Markdown文件，选择文本添加标注，让AI为您提供深度解析
        </p>
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-section">
        <q-card class="upload-card" :class="{ 'drag-active': isDragOver, 'has-file': selectedFile }"
          @dragover.prevent="onDragOver" @dragleave.prevent="onDragLeave" @drop.prevent="onDrop">
          <!-- 没有文件时显示上传界面 -->
          <div v-if="!selectedFile" class="upload-content">
            <!-- 上传图标 -->
            <div class="icon-container">
              <q-icon name="cloud_upload" class="upload-icon" :class="{ 'icon-active': isDragOver }" />
            </div>

            <!-- 主要文字 -->
            <div class="upload-text">
              <h3 class="upload-title">拖拽Markdown文件到这里</h3>
              <p class="upload-description">
                支持 .md 和 .markdown 格式文件，最大10MB
              </p>
            </div>

            <!-- 分割线 -->
            <div class="divider">
              <span class="divider-text">或者</span>
            </div>

            <!-- 选择文件按钮 -->
            <q-btn unelevated rounded color="primary" size="lg" class="select-btn" @click="selectFile">
              <q-icon name="folder_open" class="q-mr-sm" />
              选择文件
            </q-btn>
          </div>

          <!-- 有文件时显示文件信息 -->
          <div v-else class="file-content">
            <div class="file-info">
              <div class="file-icon-large">
                <q-icon name="description" color="primary" size="3rem" />
              </div>
              <div class="file-details">
                <h3 class="file-name">{{ selectedFile.name }}</h3>
                <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
                <p class="file-status">文件已选择，准备开始标注</p>
              </div>
              <q-btn flat round icon="close" color="grey-6" size="md" class="remove-btn" @click="removeFile" />
            </div>

            <!-- 操作按钮 -->
            <div class="file-actions">
              <q-btn unelevated rounded color="primary" size="lg" class="change-btn" @click="selectFile">
                <q-icon name="swap_horiz" class="q-mr-sm" />
                更换文件
              </q-btn>

              <q-btn unelevated rounded color="positive" size="xl" class="process-btn" @click="processFile">
                <q-icon name="auto_awesome" class="q-mr-sm" />
                开始智能标注
              </q-btn>
            </div>
          </div>

          <!-- 隐藏的文件选择器 -->
          <q-file ref="fileInput" v-model="selectedFile" accept=".md,.markdown" @update:model-value="onFileSelected"
            style="display: none" />
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref } from 'vue'
import { useQuasar } from 'quasar'

const $q = useQuasar()

// 响应式数据
const selectedFile = ref(null)
const isDragOver = ref(false)
const fileInput = ref(null)

// 文件选择
const selectFile = () => {
  fileInput.value.pickFiles()
}

// 文件选择事件处理
const onFileSelected = (file) => {
  if (file) {
    validateAndSetFile(file)
  }
}

// 拖拽事件处理
const onDragOver = () => {
  isDragOver.value = true
}

const onDragLeave = () => {
  isDragOver.value = false
}

const onDrop = (e) => {
  isDragOver.value = false
  const files = e.dataTransfer.files
  if (files.length > 0) {
    const file = files[0]
    validateAndSetFile(file)
  }
}

// 文件验证和设置
const validateAndSetFile = (file) => {
  const allowedTypes = ['.md', '.markdown']
  const fileName = file.name.toLowerCase()
  const isValidType = allowedTypes.some(type => fileName.endsWith(type))

  if (!isValidType) {
    $q.notify({
      type: 'negative',
      message: '请选择Markdown文件 (.md 或 .markdown)',
      position: 'top'
    })
    return
  }

  // 检查文件大小 (10MB限制)
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    $q.notify({
      type: 'negative',
      message: '文件大小不能超过10MB',
      position: 'top'
    })
    return
  }

  selectedFile.value = file
  $q.notify({
    type: 'positive',
    message: '文件选择成功',
    position: 'top'
  })
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 处理文件
const processFile = () => {
  if (!selectedFile.value) return

  $q.notify({
    type: 'info',
    message: '正在处理文件...',
    position: 'top'
  })

  // 这里可以添加文件处理逻辑
  console.log('处理文件:', selectedFile.value)
}
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem;
}

/* 内容包装器 */
.content-wrapper {
  max-width: 700px;
  margin: 0 auto;
  min-height: calc(100vh - 4rem);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 3rem;
}

.main-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
  line-height: 1.6;
  font-weight: 400;
}

/* 上传区域 */
.upload-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.upload-card {
  background: white;
  border-radius: 16px;
  border: 2px dashed #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 3rem 2rem;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.upload-card.drag-active {
  border-color: #3b82f6;
  background-color: #f8fafc;
  border-style: solid;
}

.upload-card.has-file {
  border-style: solid;
  border-color: #10b981;
  background-color: #f0fdf4;
  cursor: default;
}

.upload-card.has-file:hover {
  transform: none;
  border-color: #10b981;
  background-color: #f0fdf4;
}

/* 上传内容 */
.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

/* 图标区域 */
.icon-container {
  margin-bottom: 1.5rem;
}

.upload-icon {
  font-size: 4rem;
  color: #94a3b8;
  transition: all 0.3s ease;
}

.upload-icon.icon-active {
  color: #3b82f6;
  transform: scale(1.1);
}

/* 文字区域 */
.upload-text {
  margin-bottom: 2rem;
}

.upload-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.upload-description {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

/* 分割线 */
.divider {
  width: 100%;
  position: relative;
  margin: 1.5rem 0;
  text-align: center;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e2e8f0;
}

.divider-text {
  background: white;
  color: #94a3b8;
  padding: 0 1rem;
  font-size: 0.875rem;
  position: relative;
}

/* 按钮样式 */
.select-btn {
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.select-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15);
}

/* 文件内容 */
.file-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  gap: 2rem;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  width: 100%;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.file-icon-large {
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  word-break: break-all;
}

.file-size {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0 0 0.25rem 0;
}

.file-status {
  font-size: 0.875rem;
  color: #059669;
  margin: 0;
  font-weight: 500;
}

.remove-btn {
  flex-shrink: 0;
}

/* 文件操作按钮 */
.file-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.change-btn {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.change-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15);
}

.process-btn {
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.process-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 12px -1px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 1rem;
  }

  .content-wrapper {
    min-height: calc(100vh - 2rem);
  }

  .main-title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .upload-card {
    padding: 2rem 1.5rem;
    min-height: 250px;
  }

  .upload-icon {
    font-size: 3rem;
  }

  .upload-title {
    font-size: 1.25rem;
  }

  .file-info {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .file-details {
    text-align: center;
  }

  .file-actions {
    flex-direction: column;
    width: 100%;
  }

  .change-btn,
  .process-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 1.75rem;
  }

  .upload-card {
    padding: 1.5rem 1rem;
  }

  .file-name {
    font-size: 1.1rem;
  }
}
</style>
